import { Box, Card, CardContent, Grid, Stack, Typography } from "@mui/material";
import { nanoid } from "nanoid";
import type * as React from "react";
import IconsaxTimerStartIcon from "@/assets/icons/iconsax-timer-start.svg?react";
import IconsaxUserDoubleIcon from "@/assets/icons/iconsax-user-double.svg?react";
import { IconWrapper } from "@/shared/components/common/IconWrapper";
import { AttendanceLineChart } from "../components/AttendanceLineChart";

const usersData = [
	{
		id: nanoid(),
		title: "Total Karyawan",
		total: 128,
		change: {
			value: 0,
			description: "Sama seperti kemarin",
			color: "#666666",
			background: "#E8E9E9",
		},
		icon: {
			color: "#0090FF",
			background: "#C3E6FF",
		},
	},
	{
		id: nanoid(),
		title: "Total Karyawan Hadir",
		total: 201,
		change: {
			value: 10,
			description: "Meningkat dibanding kemarin",
			color: "#00CE11",
			background: "#C9EACB",
		},
		icon: {
			color: "#00CE11",
			background: "#C9EACB",
		},
	},
	{
		id: nanoid(),
		title: "Total Karyawan Tidak Hadir",
		total: 5,
		change: {
			value: -3,
			description: "Menurun dibanding kemarin",
			color: "#00CE11",
			background: "#C9EACB",
		},
		icon: {
			color: "#F02D2D",
			background: "#FFB8B8",
		},
	},
	{
		id: nanoid(),
		title: "Total Karyawan Cuti",
		total: 5,
		change: {
			value: 1,
			description: "Meningkat dibanding kemarin",
			color: "#F02D2D",
			background: "#FFB8B8",
		},
		icon: {
			color: "#FFC800",
			background: "#FFEEB0",
		},
	},
];

const attendanceData = [
	{
		id: nanoid(),
		title: "Total Kariawan Datang Tepat Waktu",
		total: 125,
		change: {
			value: 2,
			description: "Meningkat dibanding kemarin",
			color: "#00CE11",
			background: "#C9EACB",
		},
		icon: {
			color: "#00CE11",
			background: "#C9EACB",
		},
	},
	{
		id: nanoid(),
		title: "Total Kariawan Datang Terlambat",
		total: 3,
		change: {
			value: 1,
			description: "Meningkat dibanding kemarin",
			color: "#F02D2D",
			background: "#FFB8B8",
		},
		icon: {
			color: "#F02D2D",
			background: "#FFB8B8",
		},
	},
	{
		id: nanoid(),
		title: "Total Kariawan Pulang Tepat Waktu",
		total: 125,
		change: {
			value: 2,
			description: "Meningkat dibanding kemarin",
			color: "#00CE11",
			background: "#C9EACB",
		},
		icon: {
			color: "#00CE11",
			background: "#C9EACB",
		},
	},
	{
		id: nanoid(),
		title: "Total Kariawan Pulang Cepat",
		total: 3,
		change: {
			value: 1,
			description: "Meningkat dibanding kemarin",
			color: "#F02D2D",
			background: "#FFB8B8",
		},
		icon: {
			color: "#F02D2D",
			background: "#FFB8B8",
		},
	},
];

const DashboardPage: React.FC = () => {
	return (
		<Box sx={{ pb: 12 }}>
			<Grid container spacing={2}>
				{usersData.map((item) => {
					let trendSymbol = "~";
					if (item.change.value > 0) trendSymbol = "▲";
					else if (item.change.value < 0) trendSymbol = "▼";

					return (
						<Grid key={item.id} size={{ xs: 12, md: 6, lg: 3 }}>
							<Card sx={{ height: 200 }}>
								<CardContent
									sx={{
										height: "100%",
										display: "flex",
										flexDirection: "column",
										justifyContent: "space-between",
									}}
								>
									{/* Header */}
									<Stack direction="row" spacing={2} alignItems="center">
										<Box
											sx={{
												display: "inline-flex",
												backgroundColor: "#C3E6FF",
												color: "#0090FF",
												p: 1,
												borderRadius: 1,
											}}
										>
											<IconWrapper icon={IconsaxUserDoubleIcon} />
										</Box>
										<Typography variant="subtitle1">{item.title}</Typography>
									</Stack>

									{/* Main Content */}
									<Stack
										direction="row"
										spacing={2}
										alignItems="center"
										justifyContent="space-between"
									>
										<Typography sx={{ fontSize: "3rem" }}>
											{item.total}
										</Typography>
										<Box
											sx={{
												display: "inline-flex",
												backgroundColor: item.change.background,
												color: item.change.color,
												p: 0.5,
												borderRadius: 1,
											}}
										>
											<Typography variant="subtitle1">
												{trendSymbol} {Math.abs(item.change.value)}
											</Typography>
										</Box>
									</Stack>

									{/* Footer */}
									<Box
										sx={{
											backgroundColor: item.change.background,
											color: item.change.color,
											py: 0.5,
											px: 1,
											borderRadius: 1,
										}}
									>
										<Typography variant="subtitle2">
											{item.change.description}
										</Typography>
									</Box>
								</CardContent>
							</Card>
						</Grid>
					);
				})}
			</Grid>

			<Box sx={{ pt: 4 }}>
				<AttendanceLineChart />
			</Box>

			<Grid container spacing={2} sx={{ pt: 4 }}>
				{attendanceData.map((item) => {
					let trendSymbol = "~";
					if (item.change.value > 0) trendSymbol = "▲";
					else if (item.change.value < 0) trendSymbol = "▼";

					return (
						<Grid key={item.id} size={{ xs: 12, md: 6, lg: 3 }}>
							<Card sx={{ height: 200 }}>
								<CardContent
									sx={{
										height: "100%",
										display: "flex",
										flexDirection: "column",
										justifyContent: "space-between",
									}}
								>
									{/* Header */}
									<Stack direction="row" spacing={2} alignItems="center">
										<Box
											sx={{
												display: "inline-flex",
												backgroundColor: "#C3E6FF",
												color: "#0090FF",
												p: 1,
												borderRadius: 1,
											}}
										>
											<IconWrapper icon={IconsaxTimerStartIcon} />
										</Box>
										<Typography variant="subtitle1">{item.title}</Typography>
									</Stack>

									{/* Main Content */}
									<Stack
										direction="row"
										spacing={2}
										alignItems="center"
										justifyContent="space-between"
									>
										<Typography sx={{ fontSize: "3rem" }}>
											{item.total}
										</Typography>
										<Box
											sx={{
												display: "inline-flex",
												backgroundColor: item.change.background,
												color: item.change.color,
												p: 0.5,
												borderRadius: 1,
											}}
										>
											<Typography variant="subtitle1">
												{trendSymbol} {Math.abs(item.change.value)}
											</Typography>
										</Box>
									</Stack>

									{/* Footer */}
									<Box
										sx={{
											backgroundColor: item.change.background,
											color: item.change.color,
											py: 0.5,
											px: 1,
											borderRadius: 1,
										}}
									>
										<Typography variant="subtitle2">
											{item.change.description}
										</Typography>
									</Box>
								</CardContent>
							</Card>
						</Grid>
					);
				})}
			</Grid>
		</Box>
	);
};

export default DashboardPage;
